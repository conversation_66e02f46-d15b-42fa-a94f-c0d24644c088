The Smart City OS application is experiencing a critical error in the SensorMap component that's preventing the dashboard from loading properly. The error "Invalid LatLng object: (undefined, undefined)" indicates that the Leaflet map is receiving undefined latitude and longitude coordinates.

Please diagnose and fix this mapping error by:

1. **Investigate the SensorMap Component**:
   - Examine the `src/components/SensorMap.js` file to identify where latitude and longitude values are being set
   - Check if the sensor data being passed to the map component contains valid coordinate properties
   - Verify that the map center coordinates are properly defined and not undefined

2. **Check Data Flow and API Integration**:
   - Verify that the sensor data from the Supabase API includes valid `latitude` and `longitude` fields
   - Check if the data structure matches what the SensorMap component expects
   - Ensure that the mock/fallback sensor data also includes proper coordinates
   - Verify that the data transformation between the API response and component props is working correctly

3. **Fix Coordinate Handling**:
   - Add proper validation and fallback values for latitude/longitude coordinates
   - Implement default coordinates (e.g., for Kolkata or Kharagpur as mentioned in previous requirements)
   - Add error boundaries or conditional rendering to prevent the map from crashing when coordinates are invalid
   - Ensure that sensors without valid coordinates are either filtered out or assigned default locations

4. **Test and Validate**:
   - Test the dashboard to ensure the map loads without errors
   - Verify that sensor markers appear correctly on the map
   - Check that the map centers on the correct city (Indian city as per the theme)
   - Ensure the error doesn't reoccur when switching between different views or refreshing the page

The goal is to have a fully functional interactive map on the dashboard that displays sensor locations without any console errors, maintaining the Indian city theme we've implemented.

Also check if the Supabase setup SQL query is correct or not in supabase_setup.sql, do help updating it, so we can compleme the full setup and run the application seamlessly with out facing issues